package com.kerryprops.kip.audit;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.hibernate.event.spi.*;
import org.hibernate.persister.entity.EntityPersister;
import org.springframework.core.annotation.AnnotationUtils;

@Slf4j
@RequiredArgsConstructor
public class AuditChangeListener implements PostInsertEventListener, PreUpdateEventListener,
        PostUpdateEventListener, PreDeleteEventListener {

    private final AuditService auditService;

    public void postPersist(Object entity) {
        log.info("Auditing when persist: {}", entity);
        if (isAuditableEntity(entity)) {
            auditService.recordCreation(entity);
        }
    }

    public void preUpdate(Object entity) {
        if (isAuditableEntity(entity)) {
            auditService.beforeUpdate(entity);
        }
    }

    public void postUpdate(Object entity) {
        log.info("Auditing after update: {}", entity);
        if (isAuditableEntity(entity)) {
            auditService.recordUpdate(entity);
        }
    }

    public void postRemove(Object entity) {
        log.info("Auditing before delete: {}", entity);
        if (isAuditableEntity(entity)) {
            auditService.recordDeletion(entity);
        }
    }

    // Hibernate事件监听器方法实现
    @Override
    public void onPostInsert(PostInsertEvent event) {
        postPersist(event.getEntity());
    }

    @Override
    public boolean onPreUpdate(PreUpdateEvent event) {
        preUpdate(event.getEntity());
        return false; // 不阻止更新操作
    }

    @Override
    public void onPostUpdate(PostUpdateEvent event) {
        postUpdate(event.getEntity());
    }

    @Override
    public boolean onPreDelete(PreDeleteEvent event) {
        postRemove(event.getEntity());
        return false; // 不阻止删除操作
    }

    @Override
    public boolean requiresPostCommitHandling(EntityPersister persister) {
        return false;
    }

    /**
     * 检查实体是否可审计.
     * 只需要标注@AuditEntity注解即可。
     *
     * @param entity 实体对象
     * @return 是否可审计
     */
    private boolean isAuditableEntity(Object entity) {
        return AnnotationUtils.findAnnotation(entity.getClass(), AuditEntity.class) != null;
    }

}
