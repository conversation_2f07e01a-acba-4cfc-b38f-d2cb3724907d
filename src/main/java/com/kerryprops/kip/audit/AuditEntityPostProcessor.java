package com.kerryprops.kip.audit;

import jakarta.persistence.EntityManagerFactory;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.hibernate.event.service.spi.EventListenerRegistry;
import org.hibernate.event.spi.EventType;
import org.hibernate.internal.SessionFactoryImpl;
import org.springframework.boot.autoconfigure.AutoConfigurationPackages;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationListener;
import org.springframework.context.event.ContextRefreshedEvent;
import org.springframework.util.Assert;

import static com.kerryprops.kip.audit.AppConstants.BASE_PACKAGE;

/**
 * 审计实体后处理器.
 * 负责在Spring上下文刷新后，扫描带有@AuditEntity注解的实体类，
 * 并通过Hibernate的EventListenerRegistry全局注册审计监听器。
 *
 * <AUTHOR>
 */
@Slf4j
@RequiredArgsConstructor
public class AuditEntityPostProcessor implements ApplicationListener<ContextRefreshedEvent> {

    private final EntityManagerFactory entityManagerFactory;
    private final AuditChangeListener auditChangeListener;
    private final ApplicationContext applicationContext;
    private final AuditEntityScanner auditEntityScanner;

    private volatile boolean initialized = false;

    @Override
    public void onApplicationEvent(ContextRefreshedEvent event) {
        // 确保只初始化一次
        if (initialized || event.getApplicationContext() != this.applicationContext) {
            log.debug("审计实体后处理器已初始化或应用上下文不匹配，跳过本次初始化");
            return;
        }

        try {
            registerGlobalAuditListener();
            var scanPackages = getScanPackages();
            var auditEntityClasses = auditEntityScanner.scanAuditEntities(scanPackages);

            log.debug("审计系统已初始化，发现 {} 个审计实体:", auditEntityClasses.size());
            auditEntityClasses.forEach(clazz -> log.trace("  -> {}", clazz.getName()));

            initialized = true;
        } catch (Exception e) {
            throw new AuditEntityPostProcessorException("审计实体后处理失败", e);
        }
    }

    /**
     * 通过Hibernate的EventListenerRegistry全局注册审计监听器.
     */
    private void registerGlobalAuditListener() {
        var sessionFactory = entityManagerFactory.unwrap(SessionFactoryImpl.class);
        var registry = sessionFactory.getServiceRegistry()
                .getService(EventListenerRegistry.class);
        Assert.notNull(registry, "EventListenerRegistry cannot be null");
        // 注册各种生命周期事件的监听器
        registry.getEventListenerGroup(EventType.POST_INSERT)
                .appendListener(auditChangeListener);
        registry.getEventListenerGroup(EventType.PRE_UPDATE)
                .appendListener(auditChangeListener);
        registry.getEventListenerGroup(EventType.POST_UPDATE)
                .appendListener(auditChangeListener);
        registry.getEventListenerGroup(EventType.PRE_DELETE)
                .appendListener(auditChangeListener);
    }


    /**
     * 获取需要扫描的包路径.
     *
     * @return 扫描包路径数组
     */
    private String[] getScanPackages() {
        log.debug("开始获取应用程序包扫描路径");

        try {
            // 优先使用 Spring Boot 自动配置包路径
            var autoConfigPackages = AutoConfigurationPackages.get(applicationContext);
            if (!autoConfigPackages.isEmpty()) {
                var packages = autoConfigPackages.toArray(new String[0]);
                log.info("使用 Spring Boot 自动配置包路径进行审计实体扫描: {}", String.join(", ", packages));
                return packages;
            }
        } catch (Exception e) {
            log.debug("获取自动配置包路径失败: {}", e.getMessage());
        }

        // 最终默认扫描路径
        log.warn("未找到任何可用的包扫描路径，使用默认扫描路径: {}", BASE_PACKAGE);
        return new String[]{BASE_PACKAGE};
    }

    /**
     * 判断是否为主配置类
     * 排除CGLIB代理类和其他非主配置类
     */
    private boolean isMainConfigurationClass(Class<?> beanType) {
        var className = beanType.getName();
        // 排除CGLIB代理类
        if (className.contains("$$")) {
            return false;
        }
        // 检查是否包含Application且有@SpringBootApplication或@Configuration注解
        if (className.contains("Application")) {
            return beanType.isAnnotationPresent(org.springframework.boot.autoconfigure.SpringBootApplication.class) ||
                    beanType.isAnnotationPresent(org.springframework.context.annotation.Configuration.class);
        }
        return false;
    }
}
