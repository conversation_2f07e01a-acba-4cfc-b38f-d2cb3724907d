package com.kerryprops.kip.audit;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.lang.Nullable;
import org.springframework.util.Assert;


import java.util.List;
import java.util.Optional;

/**
 * Factory for creating audit event requests.
 * This class is responsible for creating and populating audit event request
 * objects with entity information, user context, and request details.
 */
@Slf4j
@RequiredArgsConstructor
public class AuditEventFactory {

    /** Audit configuration properties. */
    private final AuditProperties auditProperties;

    /** Entity cache manager for ID resolution. */
    @Nullable
    private final EntityCacheManager entityCacheManager;

    /** Application name from configuration. */
    @Nullable
    @Value("${spring.application.name:UNKOWN-SERVICE}")
    private String applicationName;

    /**
     * Creates an audit event request object.
     *
     * @param entity the entity being audited
     * @param operation the audit operation being performed
     * @return populated audit event request
     */
    public AuditEventRequest createAuditEventRequest(final Object entity,
                                                     final AuditOperation operation) {
        var request = new AuditEventRequest();

        request.setEntityType(entity.getClass().getSimpleName());
        request.setEntityId(entityCacheManager != null
                ? entityCacheManager.findEntityId(entity) : "unknown");
        request.setOperation(operation);

        setUserInfo(request);
        setRequestInfo(request);

        return request;
    }

    /**
     * Sets user information in the audit event request.
     *
     * @param request the audit event request to populate
     */
    private void setUserInfo(final AuditEventRequest request) {
        Assert.notNull(request, "AuditEventRequest cannot be null");
        request.setUserId(AuditContext.getCurrentUserId());
        request.setUserNickname(AuditContext.getCurrentUserNickname());
    }

    /**
     * Sets request-related information in the audit event request.
     *
     * @param request the audit event request to populate
     */
    private void setRequestInfo(final AuditEventRequest request) {
        Assert.notNull(request, "AuditEventRequest cannot be null");

        request.setServiceId(auditProperties.getServiceId());

        Optional.of(AuditContext.getAuditRequestInfo())
                .ifPresent(requestInfo -> {
                    request.setIpAddress(requestInfo.getIpAddress());
                    request.setUserAgent(requestInfo.getUserAgent());
                    request.setConversationId(requestInfo.getConversationId());
                    request.setCorrelationId(requestInfo.getCorrelationId());
                    request.setUiModel(requestInfo.getUiModel());

                    request.setFilters(
                            List.of(createAuditFilters(requestInfo)));
                });
    }

    /**
     * Creates audit filter from request information.
     *
     * @param requestInfo the request information containing filter data
     * @return populated audit event filter
     */
    private AuditEventRequest.AuditEventFilter createAuditFilters(
            final AuditRequestInfo requestInfo) {
        Assert.notNull(requestInfo, "AuditRequestInfo cannot be null");
        Assert.notNull(requestInfo.getAuditFilterKey(),
                "Audit filter key is required");
        Assert.notNull(requestInfo.getAuditFilterValue(),
                "Audit filter value is required");
        var filter = new AuditEventRequest.AuditEventFilter();
        filter.setFilterKey(requestInfo.getAuditFilterKey());
        filter.setFilterValue(requestInfo.getAuditFilterValue());
        return filter;
    }
}