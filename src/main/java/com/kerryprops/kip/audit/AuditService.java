package com.kerryprops.kip.audit;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.lang.Nullable;


/**
 * Service for recording audit events for entity lifecycle operations.
 * This service handles creation, update, and deletion audit events,
 * managing entity state caching and change detection.
 */
@Slf4j
@RequiredArgsConstructor
public final class AuditService {

    /** Entity cache manager for storing original entity states. */
    @Nullable
    private final EntityCacheManager entityCacheManager;

    /** Change detector for identifying field-level changes. */
    private final ChangeDetector changeDetector;

    /** Factory for creating audit event requests. */
    private final AuditEventFactory auditEventFactory;

    /** Queue for processing audit events asynchronously. */
    private final AuditQueue auditQueue;

    /**
     * Called before entity update to cache the original entity state.
     *
     * @param entity the entity to be updated
     */
    public void beforeUpdate(final Object entity) {
        if (entityCacheManager != null) {
            entityCacheManager.cacheOriginalEntity(entity);
        } else {
            log.debug("EntityCacheManager unavailable, skipping original "
                    + "entity state caching: {}",
                    entity.getClass().getSimpleName());
        }
    }

    /**
     * Records entity creation audit event.
     *
     * @param entity the created entity
     */
    public void recordCreation(final Object entity) {
        try {
            var auditLog = auditEventFactory.createAuditEventRequest(entity,
                    AuditOperation.CREATE);
            var fieldChanges = changeDetector.detectChanges(null, entity);
            auditLog.setFieldChanges(fieldChanges);
            auditQueue.enqueue(auditLog);
        } catch (Exception e) {
            log.error("Error recording entity creation: ", e);
        }
    }

    /**
     * Records entity update audit event.
     *
     * @param entity the updated entity
     */
    public void recordUpdate(final Object entity) {
        try {
            Object oldEntity = fetchOriginalEntityWithFallback(entity);
            var auditLog = auditEventFactory.createAuditEventRequest(entity,
                    AuditOperation.UPDATE);
            var fieldChanges = changeDetector.detectChanges(oldEntity, entity);
            if (fieldChanges.isEmpty()) {
                log.warn("No field changes detected for entity: {}", entity);
                return; // Skip audit logging if no field changes
            }
            auditLog.setFieldChanges(fieldChanges);
            auditQueue.enqueue(auditLog);
        } catch (Exception e) {
            log.error("Error recording entity update: ", e);
        }
    }

    /**
     * Fetches original entity state with robust fallback strategy.
     * Prioritizes EntityCacheManager, falls back to current entity if
     * unavailable.
     *
     * @param entity current entity
     * @return original entity state, or current entity state during fallback
     *         to avoid null comparisons
     */
    private Object fetchOriginalEntityWithFallback(final Object entity) {
        // 1. First check if EntityCacheManager is available
        if (entityCacheManager == null) {
            log.warn("EntityCacheManager unavailable - possible causes: test "
                    + "environment, bean creation order issues, or "
                    + "configuration errors");
            log.info("Fallback handling: using current entity state as "
                    + "baseline, this update will not produce change records");
            return entity; // Return current entity to avoid null vs entity
        }

        try {
            // 2. Try to get original entity from EntityCacheManager
            Object originalEntity = entityCacheManager.getOriginalEntity(entity);

            // 3. Check if EntityCacheManager returned current entity
            // (indicates cache miss fallback handling)
            if (originalEntity == entity) {
                log.debug("EntityCacheManager cache miss, fallback strategy "
                        + "used");
            }

            return originalEntity;
        } catch (Exception e) {
            log.error("Failed to get original entity from "
                    + "EntityCacheManager: {}, id: {}, using fallback strategy",
                    entity.getClass().getSimpleName(),
                    entityCacheManager != null
                            ? entityCacheManager.findEntityId(entity)
                            : "unknown", e);
            log.info("Fallback handling: using current entity state as "
                    + "baseline, this update will not produce change records");
            return entity; // Fallback strategy: return current entity
        }
    }

    /**
     * Records entity deletion audit event.
     *
     * @param entity the deleted entity
     */
    public void recordDeletion(final Object entity) {
        try {
            AuditEventRequest auditLog = auditEventFactory
                    .createAuditEventRequest(entity, AuditOperation.DELETE);
            var fieldChanges = changeDetector.detectChanges(entity, null);
            auditLog.setFieldChanges(fieldChanges);
            auditQueue.enqueue(auditLog);
        } catch (Exception e) {
            log.error("Error recording entity deletion: ", e);
        }
    }

}