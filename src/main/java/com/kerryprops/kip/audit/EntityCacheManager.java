package com.kerryprops.kip.audit;

import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.github.benmanes.caffeine.cache.RemovalCause;
import jakarta.persistence.EntityManager;
import jakarta.persistence.EntityManagerFactory;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.Assert;
import org.springframework.util.ReflectionUtils;

import java.lang.reflect.Field;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.TimeUnit;

/**
 * Manager for caching entity states and ID field information.
 * Provides functionality to cache original entity states before updates
 * and efficiently resolve entity ID fields using reflection caching.
 */
@Slf4j
@RequiredArgsConstructor
public class EntityCacheManager {

    /** Maximum size for ID field cache. */
    private static final int ID_FIELD_CACHE_SIZE = 500;

    /** Expiration time for ID field cache in minutes. */
    private static final int ID_FIELD_CACHE_EXPIRY_MINUTES = 30;

    /** Maximum size for original entity cache. */
    private static final int ORIGINAL_ENTITY_CACHE_SIZE = 1000;

    /** Expiration time for original entity cache in minutes. */
    private static final int ORIGINAL_ENTITY_CACHE_EXPIRY_MINUTES = 5;

    /**
     * Entity class ID field cache to avoid repeated reflection lookups.
     * Key: entity class, Value: ID field
     */
    private static final Cache<Class<?>, Field> ID_FIELD_CACHE =
            Caffeine.newBuilder()
                    .maximumSize(ID_FIELD_CACHE_SIZE)
                    .expireAfterWrite(ID_FIELD_CACHE_EXPIRY_MINUTES,
                            TimeUnit.MINUTES)
                    .recordStats()
                    .removalListener((Class<?> key, Field value,
                                      RemovalCause cause) ->
                            log.debug("ID field cache removal - class: {}, "
                                    + "reason: {}",
                                    key != null ? key.getName() : "null",
                                    cause))
                    .build();

    /**
     * Cache for storing entity states before updates.
     * Key: entityClass:entityId, Value: entity JSON string representation
     */
    private static final Cache<String, String> ORIGINAL_ENTITY_CACHE =
            Caffeine.newBuilder()
                    .expireAfterWrite(ORIGINAL_ENTITY_CACHE_EXPIRY_MINUTES,
                            TimeUnit.MINUTES)
                    .maximumSize(ORIGINAL_ENTITY_CACHE_SIZE)
                    .recordStats()
                    .removalListener((String key, String value,
                                      RemovalCause cause) ->
                            log.debug("Cache removal - key: {}, reason: {}",
                                    key, cause))
                    .build();

    /** Entity manager factory for database operations. */
    private final EntityManagerFactory entityManagerFactory;

    /**
     * Called before entity update to query and cache the original state
     * from database.
     * Bypasses JPA first-level cache and transaction isolation to get the
     * true database original state.
     *
     * @param entity the entity to be updated
     */
    public void cacheOriginalEntity(final Object entity) {
        try {
            String entityId = findEntityId(entity);
            Object originalEntity = queryOriginalEntityByNativeSQL(entity);
            log.debug("缓存实体更新前状态: {}", originalEntity);
            String originalJson = JsonUtils.toJson(originalEntity);
            String cacheKey = generateCacheKey(entity);
            ORIGINAL_ENTITY_CACHE.put(cacheKey, originalJson);

            log.debug("缓存实体原始状态 - 键: {}, 数据长度: {}", cacheKey, originalJson.length());

            var stats = ORIGINAL_ENTITY_CACHE.stats();
            if (stats.requestCount() % 100 == 0) {
                log.info("原始实体缓存统计 - 命中率: {}%, 请求次数: {}, 命中次数: {}, 缓存大小: {}",
                        String.format("%.2f", stats.hitRate() * 100),
                        stats.requestCount(),
                        stats.hitCount(),
                        ORIGINAL_ENTITY_CACHE.estimatedSize());
            }

            log.debug("已通过原生 SQL 缓存实体原始状态: {}, id: {}",
                    entity.getClass().getSimpleName(), entityId);
        } catch (Exception e) {
            log.error("查询实体原始状态失败: {}", entity.getClass().getSimpleName(), e);
        }
    }

    /**
     * 获取原始实体状态
     * 优先从缓存获取，如果缓存未命中则使用当前实体状态作为基准
     *
     * @param entity 当前实体
     * @return 原始实体状态，降级返回当前实体状态
     */
    public Object getOriginalEntity(Object entity) {
        Assert.notNull(entity, "Entity cannot be null");
        String key = generateCacheKey(entity);
        String originalJson = ORIGINAL_ENTITY_CACHE.getIfPresent(key);

        log.debug("缓存查询 - 键: {}, 命中: {}", key, originalJson != null);

        if (originalJson == null) {
            log.warn("缓存未命中实体原始状态: {}, id: {} - 可能原因：直接构造实体、缓存过期或分布式环境",
                    entity.getClass().getSimpleName(), findEntityId(entity));
            log.info("降级处理：使用当前实体状态作为基准，本次更新将不会产生变更记录");

            return entity;
        }

        var originalEntity = JsonUtils.fromJson(originalJson, entity.getClass())
                .orElse(null);

        if (originalEntity == null) {
            log.error("反序列化原始实体失败: {}, id: {}, 使用当前实体作为降级",
                    entity.getClass().getSimpleName(), findEntityId(entity));
            return entity;
        }

        return originalEntity;
    }

    /**
     * 使用独立的 EntityManager 查询实体的数据库原始状态
     * 创建全新的 EntityManager 实例，彻底隔离当前持久化上下文的干扰
     */
    private Object queryOriginalEntityByNativeSQL(Object entity) {
        Class<?> entityClass = entity.getClass();
        Object entityId = getEntityIdValue(entity);

        // 检查是否为JPA实体，如果不是则直接返回当前实体
        if (!isJpaEntity(entityClass)) {
            log.debug("实体 {} 不是JPA实体，跳过Hibernate查询", entityClass.getSimpleName());
            return entity;
        }

        try (EntityManager freshEntityManager = entityManagerFactory.createEntityManager()) {
            return freshEntityManager.find(entityClass, entityId);
        } catch (Exception e) {
            log.error("查询实体原始状态失败: {}, id: {}", entityClass.getSimpleName(), entityId, e);
            return entity;
        }
    }

    /**
     * 查找实体ID字段值
     */
    public String findEntityId(Object entity) {
        Class<?> clazz = entity.getClass();

        Field idField = ID_FIELD_CACHE.get(clazz, entityClass -> {
            for (Field field : entityClass.getDeclaredFields()) {
                log.debug("Found field: {} in entity: {}", field.getName(), entityClass.getName());
                if (field.isAnnotationPresent(jakarta.persistence.Id.class)) {
                    ReflectionUtils.makeAccessible(field);
                    return field;
                }
            }
            log.error("Entity {} has no id field", entityClass.getName());
            throw new IllegalStateException("Entity " + entityClass.getName() + " has no id field");
        });

        return Optional.ofNullable(ReflectionUtils.getField(idField, entity))
                .map(Objects::toString)
                .orElse("");
    }

    /**
     * 获取实体ID的实际值
     */
    private Object getEntityIdValue(Object entity) {
        Class<?> clazz = entity.getClass();
        Field idField = ID_FIELD_CACHE.get(clazz, entityClass -> {
            for (Field field : entityClass.getDeclaredFields()) {
                if (field.isAnnotationPresent(jakarta.persistence.Id.class)) {
                    ReflectionUtils.makeAccessible(field);
                    return field;
                }
            }
            throw new IllegalStateException("Entity " + entityClass.getName() + " has no id field");
        });

        return Objects.requireNonNull(ReflectionUtils.getField(idField, entity));
    }

    /**
     * 生成实体缓存键
     */
    private String generateCacheKey(Object entity) {
        return entity.getClass().getName() + AppConstants.CACHE_KEY_SEPARATOR + findEntityId(entity);
    }

    /**
     * 检查是否为JPA实体
     */
    private boolean isJpaEntity(Class<?> entityClass) {
        return entityClass.isAnnotationPresent(jakarta.persistence.Entity.class);
    }
}