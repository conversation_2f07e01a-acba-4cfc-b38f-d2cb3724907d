/**
 * Comprehensive JPA Auditing System with Field-Level Change Tracking.
 *
 * <p>This package provides a complete auditing solution for Spring Boot applications,
 * offering field-level change tracking, entity lifecycle monitoring, and integration
 * with both JPA and MyBatis frameworks.</p>
 */
@NonNullApi
@NonNullFields
package com.kerryprops.kip.audit;

import org.springframework.lang.NonNullApi;
import org.springframework.lang.NonNullFields;