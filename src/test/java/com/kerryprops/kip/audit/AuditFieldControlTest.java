package com.kerryprops.kip.audit;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;

import static org.assertj.core.api.Assertions.assertThat;

/**
 * 字段审计控制功能测试.
 * 测试字段别名解析和字段过滤逻辑的各种场景
 *
 * <AUTHOR>
 */
class AuditFieldControlTest {

    private FieldAnalyzer fieldAnalyzer;
    private AuditProperties auditProperties;

    @BeforeEach
    void setUp() {
        // 设置测试配置
        auditProperties = new AuditProperties();
        Set<String> excludedFields = new HashSet<>();
        excludedFields.add("globalExcluded");
        auditProperties.setExcludedFields(excludedFields);

        Map<String, String> fieldAliases = new HashMap<>();
        fieldAliases.put("globalField", "全局配置字段");
        auditProperties.setFieldAliases(fieldAliases);
        auditProperties.setDefaultInclude(true);

        // 创建 FieldAnalyzer 用于测试
        fieldAnalyzer = new FieldAnalyzer(auditProperties);
    }

    @Test
    @DisplayName("应该正确解析字段显示名称的优先级")
    void shouldResolveFieldDisplayNameWithCorrectPriority() {
        // When & Then - 测试字段别名解析
        // 1. 注解别名优先级最高
        String annotatedFieldAlias = fieldAnalyzer.resolveFieldDisplayName(TestEntity.class, "annotatedField");
        assertThat(annotatedFieldAlias).isEqualTo("注解字段别名");

        // 2. 全局配置别名
        String globalFieldAlias = fieldAnalyzer.resolveFieldDisplayName(TestEntity.class, "globalField");
        assertThat(globalFieldAlias).isEqualTo("全局配置字段");

        // 3. 驼峰转换（默认）
        String camelCaseAlias = fieldAnalyzer.resolveFieldDisplayName(TestEntity.class, "normalField");
        assertThat(camelCaseAlias).isEqualTo("Normal Field");
    }

    @Test
    @DisplayName("应该正确执行字段审计过滤策略")
    void shouldApplyFieldAuditFilteringCorrectly() {
        // When & Then - 测试字段过滤逻辑
        // 1. 全局排除字段应该被跳过
        boolean globalExcludedSkipped = fieldAnalyzer.shouldSkipAuditField(TestEntity.class, "globalExcluded");
        assertThat(globalExcludedSkipped).isTrue();

        // 2. 类级排除字段应该被跳过
        boolean classExcludedSkipped = fieldAnalyzer.shouldSkipAuditField(TestEntity.class, "classExcludedField");
        assertThat(classExcludedSkipped).isTrue();

        // 3. 注解排除字段应该被跳过
        boolean annotationExcludedSkipped = fieldAnalyzer.shouldSkipAuditField(TestEntity.class, "annotationExcludedField");
        assertThat(annotationExcludedSkipped).isTrue();

        // 4. 正常字段不应该被跳过
        boolean normalFieldSkipped = fieldAnalyzer.shouldSkipAuditField(TestEntity.class, "normalField");
        assertThat(normalFieldSkipped).isFalse();

        // 5. 注解包含字段不应该被跳过
        boolean annotationIncludedSkipped = fieldAnalyzer.shouldSkipAuditField(TestEntity.class, "annotatedField");
        assertThat(annotationIncludedSkipped).isFalse();
    }

    @Test
    @DisplayName("白名单模式应该只包含指定字段")
    void shouldOnlyIncludeWhitelistedFields() {
        // When & Then - 测试白名单逻辑
        // 1. 白名单内的字段不应该被跳过
        boolean includedFieldSkipped = fieldAnalyzer.shouldSkipAuditField(WhitelistEntity.class, "includedField");
        assertThat(includedFieldSkipped).isFalse();

        // 2. 不在白名单内的字段应该被跳过
        boolean notInWhitelistSkipped = fieldAnalyzer.shouldSkipAuditField(WhitelistEntity.class, "notInWhitelist");
        assertThat(notInWhitelistSkipped).isTrue();
    }

    @Test
    @DisplayName("默认排除策略应该工作")
    void shouldApplyDefaultExcludeStrategy() {
        // When & Then - 测试默认排除策略
        // 1. 默认排除策略下，正常字段应该被跳过
        boolean normalFieldSkipped = fieldAnalyzer.shouldSkipAuditField(DefaultExcludeEntity.class, "normalField");
        assertThat(normalFieldSkipped).isTrue();

        // 2. 显式包含字段不应该被跳过
        boolean explicitIncludeSkipped = fieldAnalyzer.shouldSkipAuditField(DefaultExcludeEntity.class, "explicitIncludeField");
        assertThat(explicitIncludeSkipped).isFalse();
    }

    /**
     * 测试实体类 - 包含各种字段审计注解和配置
     */
    @AuditEntity(excludeFields = {"classExcludedField"})
    static class TestEntity {
        @AuditField(alias = "ID")
        private Long id;

        @AuditField(alias = "注解字段别名", include = true)
        private String annotatedField;

        private String globalField;

        private String normalField;

        @AuditField(exclude = true)
        private String annotationExcludedField;

        // 全局配置排除的字段
        private String globalExcluded;

        // 类级注解排除的字段
        private String classExcludedField;
    }

    /**
     * 白名单测试实体 - 只审计指定字段
     */
    @AuditEntity(includeFields = {"includedField"})
    static class WhitelistEntity {
        @AuditField(alias = "ID")
        private Long id;

        private String includedField;
        private String notInWhitelist;
    }

    /**
     * 默认排除测试实体 - 默认不审计字段
     */
    @AuditEntity(defaultInclude = false)
    static class DefaultExcludeEntity {
        @jakarta.persistence.Id
        private Long id;

        private String normalField;

        @AuditField(include = true)
        private String explicitIncludeField;
    }
}