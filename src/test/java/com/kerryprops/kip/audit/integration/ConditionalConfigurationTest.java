package com.kerryprops.kip.audit.integration;

import com.kerryprops.kip.audit.*;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.Import;
import org.springframework.test.context.TestPropertySource;

import static org.assertj.core.api.Assertions.assertThat;

/**
 * 条件配置功能测试.
 * 测试AuditAutoConfiguration的条件配置机制
 *
 * <AUTHOR>
 */
@SpringBootTest(classes = ConditionalConfigurationTest.TestApplicationWithoutAuditService.class)
@TestPropertySource(properties = {
        "audit.enabled=true",
        "spring.application.name=test-app"
})
@DisplayName("条件配置功能测试")
class ConditionalConfigurationTest {

    @SpringBootApplication(scanBasePackages = {"com.kerryprops.kip.audit.integration"})
    @MapperScan(basePackages = "com.kerryprops.kip.audit.mybatis")
    @Import(AuditAutoConfiguration.class)
    static class TestApplicationWithoutAuditService {
        // 这个配置类故意不扫描包含AuditService的包，从而不会创建AuditService Bean
    }

    @Test
    @DisplayName("验证AuditChangeListener的条件创建机制")
    void shouldVerifyAuditChangeListenerConditionalCreation(ApplicationContext applicationContext) {
        // 验证AuditAutoConfiguration被创建
        assertThat(applicationContext.getBean(AuditAutoConfiguration.class))
                .isNotNull();

        // 检查AuditService是否存在 - 如果存在，则AuditChangeListener也应该存在
        var auditServiceExists = applicationContext.containsBean("auditService");
        // 验证MyBatis审计组件存在
        var myBatisInterceptorExists = applicationContext.containsBean("myBatisAuditInterceptor");

        // 验证条件依赖：如果AuditService存在，MyBatis组件也应该存在
        if (auditServiceExists) {
            assertThat(myBatisInterceptorExists).isTrue();
        }
    }
}