package com.kerryprops.kip.audit.integration;

import com.kerryprops.kip.audit.AuditAutoConfiguration;
import com.kerryprops.kip.audit.AuditService;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.Import;
import org.springframework.test.context.TestPropertySource;

import static org.assertj.core.api.Assertions.assertThat;

/**
 * 测试实体扫描和注册功能
 */
@SpringBootTest(classes = EntityScanConfigurationTest.TestApplication.class)
@TestPropertySource(properties = {
        "audit.enabled=true",
        "logging.level.com.kerryprops.kip.audit=DEBUG",
        "spring.application.name=test-app"
})
@DisplayName("实体扫描和注册功能测试")
class EntityScanConfigurationTest {

    @SpringBootApplication(scanBasePackages = "com.kerryprops.kip.audit")
    @MapperScan(basePackages = "com.kerryprops.kip.audit.mybatis")
    @Import(AuditAutoConfiguration.class)
    static class TestApplication {
    }


    @Test
    @DisplayName("应该正确配置审计自动配置")
    void shouldConfigureAuditAutoConfiguration(ApplicationContext applicationContext) {
        var auditAutoConfiguration = applicationContext.getBean(AuditAutoConfiguration.class);
        assertThat(auditAutoConfiguration).isNotNull();

        // 验证审计服务被正确创建
        var auditService = applicationContext.getBean(AuditService.class);
        assertThat(auditService).isNotNull();
    }

    @Test
    @DisplayName("应该正确处理已有EntityListeners注解的实体")
    void shouldHandleEntitiesWithExistingEntityListeners(ApplicationContext applicationContext) {
        var auditAutoConfiguration = applicationContext.getBean(AuditAutoConfiguration.class);
        assertThat(auditAutoConfiguration).isNotNull();

        // 验证配置类能够正确初始化，即使实体已有EntityListeners注解
        // 这个测试主要验证不会抛出异常
    }
}
